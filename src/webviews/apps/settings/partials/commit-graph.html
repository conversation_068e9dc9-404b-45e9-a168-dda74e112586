<section id="commit-graph" class="section--settings section--collapsible" tabindex="-1">
	<div class="section__header">
		<h2>
			Commit Graph&nbsp;<gl-feature-badge></gl-feature-badge>
			<a
				class="link__learn-more"
				title="Learn more"
				href="https://help.gitkraken.com/gitlens/gitlens-plus/#commit-graph"
			>
				<i class="icon icon__info"></i>
			</a>
		</h2>

		<p class="section__header-hint">
			Adds a
			<a class="command command--show-view" title="Show Commit Graph" href="command:gitlens.showGraph"
				>Commit Graph</a
			>
			to visualize, explore, and manage a Git repository
		</p>
	</div>

	<div class="section__collapsible">
		<div class="section__group">
			<div class="section__content">
				<div class="settings settings--fixed ml-1">
					<div class="setting">
						<div class="setting__input">
							<label for="graph.layout">Prefer showing the Commit Graph in the </label>
							<div class="select-container">
								<select id="graph.layout" name="graph.layout" data-setting>
									<option value="editor">editor area</option>
									<option value="panel">bottom panel</option>
								</select>
							</div>
						</div>
					</div>

					<div class="setting">
						<div class="setting__input">
							<label for="graph.defaultItemLimit">Show </label>
							<input
								id="graph.defaultItemLimit"
								name="graph.defaultItemLimit"
								type="number"
								placeholder="500"
								data-setting
								data-default-value="500"
							/>
							<label for="graph.defaultItemLimit"> rows at first and</label>
							<label for="graph.pageItemLimit">&nbsp;then page in </label>
							<input
								id="graph.pageItemLimit"
								name="graph.pageItemLimit"
								type="number"
								placeholder="200"
								data-setting
								data-default-value="200"
							/>
							<label for="graph.pageItemLimit"> more rows when scrolling</label>
						</div>
					</div>

					<div class="setting">
						<div class="setting__input">
							<label for="graph.searchItemLimit">Show </label>
							<input
								id="graph.searchItemLimit"
								name="graph.searchItemLimit"
								type="number"
								placeholder="100"
								data-setting
								data-default-value="100"
							/>
							<label for="graph.searchItemLimit"> search results at first and when paging</label>
						</div>
					</div>

					<div class="setting">
						<div class="setting__input">
							<label for="graph.scrollRowPadding">Start scrolling at </label>
							<input
								id="graph.scrollRowPadding"
								name="graph.scrollRowPadding"
								type="number"
								placeholder="0"
								data-setting
								data-default-value="0"
							/>
							<label for="graph.scrollRowPadding"> rows from the edge</label>
						</div>
					</div>

					<div class="setting">
						<div class="setting__input">
							<input
								id="graph.scrollMarkers.enabled"
								name="graph.scrollMarkers.enabled"
								type="checkbox"
								data-setting
							/>
							<label for="graph.scrollMarkers.enabled">Show markers on the Commit Graph scrollbar</label>
						</div>
					</div>

					<div class="setting">
						<div class="setting__input setting__input--inner-select">
							<input
								id="graph.showDetailsView"
								name="graph.showDetailsView"
								type="checkbox"
								value="selection"
								data-setting
							/>
							<label for="graph.showDetailsView">Show Inspect view</label>
							<div class="select-container">
								<select
									id="graph.showDetailsView"
									name="graph.showDetailsView"
									data-setting
									data-enablement="graph.showDetailsView !false"
									disabled
								>
									<option value="open">only when opening</option>
									<option value="selection">when selection changes (default)</option>
								</select>
							</div>
						</div>
					</div>

					<div class="setting">
						<div class="setting__input">
							<input
								id="graph.showGhostRefsOnRowHover"
								name="graph.showGhostRefsOnRowHover"
								type="checkbox"
								data-setting
							/>
							<label for="graph.showGhostRefsOnRowHover"
								>Show ghost branch / tag when hovering over or selecting a commit</label
							>
						</div>
					</div>

					<div class="setting">
						<div class="setting__input">
							<input
								id="graph.highlightRowsOnRefHover"
								name="graph.highlightRowsOnRefHover"
								type="checkbox"
								data-setting
							/>
							<label for="graph.highlightRowsOnRefHover"
								>Highlight associated rows when hovering over a branch</label
							>
						</div>
					</div>

					<div class="setting">
						<div class="setting__input">
							<input
								id="graph.dimMergeCommits"
								name="graph.dimMergeCommits"
								type="checkbox"
								data-setting
							/>
							<label for="graph.dimMergeCommits">Dim merge commit rows</label>
						</div>
					</div>

					<div class="setting">
						<div class="setting__input">
							<input
								id="graph.showRemoteNames"
								name="graph.showRemoteNames"
								type="checkbox"
								data-setting
							/>
							<label for="graph.showRemoteNames">Show remote names on remote branches</label>
						</div>
					</div>

					<div class="setting">
						<div class="setting__input">
							<input
								id="graph.showUpstreamStatus"
								name="graph.showUpstreamStatus"
								type="checkbox"
								data-setting
							/>
							<label for="graph.showUpstreamStatus"
								>Show upstream status on local branches with remotes</label
							>
						</div>
					</div>

					<div class="setting">
						<div class="setting__input">
							<input id="graph.issues.enabled" name="graph.issues.enabled" type="checkbox" data-setting />
							<label for="graph.issues.enabled">Show associated issues on branches</label>
						</div>
						<p class="setting__hint hidden" data-visibility="graph.issues.enabled">
							<i class="icon icon__info"></i>Requires a connection to a supported issue service (e.g.
							GitHub)
						</p>
					</div>

					<div class="setting">
						<div class="setting__input">
							<input
								id="graph.pullRequests.enabled"
								name="graph.pullRequests.enabled"
								type="checkbox"
								data-setting
							/>
							<label for="graph.pullRequests.enabled"
								>Show associated pull requests on remote branches</label
							>
						</div>
						<p class="setting__hint hidden" data-visibility="graph.pullRequests.enabled">
							<i class="icon icon__info"></i>Requires a connection to a supported remote service (e.g.
							GitHub)
						</p>
					</div>

					<div class="setting">
						<div class="setting__input">
							<input id="graph.avatars" name="graph.avatars" type="checkbox" data-setting />
							<label for="graph.avatars">Use author and remote avatars</label>
						</div>
					</div>

					<div class="setting">
						<div class="setting__input">
							<input
								id="graph.dateStyle"
								name="graph.dateStyle"
								type="checkbox"
								value="relative"
								data-value-off="absolute"
								data-setting
							/>
							<label for="graph.dateStyle">Allow relative date formatting</label>
						</div>
						<span class="setting__hint hidden" data-visibility="graph.dateStyle =relative"
							>Shows some dates relatively, e.g. 1 day ago</span
						>
						<span
							class="setting__hint hidden"
							data-visibility="graph.dateStyle =null &amp; defaultDateStyle =relative"
							>Shows some dates relatively, e.g. 1 day ago</span
						>
						<span class="setting__hint hidden" data-visibility="graph.dateStyle =absolute"
							>Shows dates absolutely, e.g.
							<span
								data-setting-preview="graph.dateFormat"
								data-setting-preview-type="date"
								data-setting-preview-default="MMMM Do, YYYY h:mma"
								data-setting-preview-default-lookup="defaultDateFormat"
							></span>
						</span>
						<span
							class="setting__hint hidden"
							data-visibility="graph.dateStyle =null &amp; defaultDateStyle =absolute"
							>Shows dates absolutely, e.g.
							<span
								data-setting-preview="graph.dateFormat"
								data-setting-preview-type="date"
								data-setting-preview-default="MMMM Do, YYYY h:mma"
								data-setting-preview-default-lookup="defaultDateFormat"
							></span>
						</span>
					</div>

					<div class="setting">
						<div class="setting__input">
							<label for="graph.dateFormat">Date&nbsp;format</label>
							<input
								id="graph.dateFormat"
								name="graph.dateFormat"
								type="text"
								placeholder="defaults to `defaultDateFormat` value"
								data-setting
								data-setting-preview
							/>
							<a
								class="link__learn-more"
								title="See Moment.js docs for supported date formats"
								href="https://momentjs.com/docs/#/displaying/format/"
							>
								<i class="icon icon__info"></i>
							</a>
						</div>
						<span class="setting__hint"
							>Example date:
							<span
								data-setting-preview="graph.dateFormat"
								data-setting-preview-type="date"
								data-setting-preview-default="MMMM Do, YYYY h:mma"
								data-setting-preview-default-lookup="defaultDateFormat"
							></span>
						</span>
					</div>
				</div>
			</div>

			<div class="section__preview">
				<img
					class="image__preview"
					src="#{webroot}/media/plus-commit-graph-illustrated.webp"
					loading="lazy"
					width="850"
					height="450"
				/>
			</div>
		</div>

		<div class="section__group">
			<p class="section__hint">
				<i class="icon icon__info"></i> For more options, open the
				<a
					class="secondary"
					title="Open Settings UI"
					href="command:workbench.action.openSettings?%22gitlens.graph%22"
					>Settings UI</a
				>
				and search for <b><i>gitlens.graph</i></b>
			</p>
		</div>
	</div>
</section>
