<section id="branches-view" class="section--settings section--collapsible" tabindex="-1">
	<div class="section__header">
		<h2>
			Branches view
			<a
				class="link__learn-more"
				title="Learn more"
				href="https://help.gitkraken.com/gitlens/side-bar/#branches-view"
			>
				<i class="icon icon__info"></i>
			</a>
		</h2>

		<p class="section__header-hint">
			Adds a
			<a class="command command--show-view" title="Show View in Side Bar" href="command:gitlens.showBranchesView"
				>Branches view</a
			>
			to visualize, explore, and manage Git branches
		</p>
	</div>

	<div class="section__collapsible">
		<div class="section__group">
			<div class="section__content">
				<div class="settings settings--fixed ml-1">
					<div class="setting">
						<div class="setting__input">
							<input
								id="views.branches.showBranchComparison"
								name="views.branches.showBranchComparison"
								type="checkbox"
								value="branch"
								data-setting
							/>
							<label for="views.branches.showBranchComparison"
								>Show a comparison of the branch with a user-selected reference (branch, tag, etc)
							</label>
						</div>
					</div>

					<div class="section__group">
						<div class="section__content">
							<div class="settings settings--fixed">
								<div class="setting">
									<div class="setting__input">
										<input
											id="views.branches.pullRequests.enabled"
											name="views.branches.pullRequests.enabled"
											type="checkbox"
											data-setting
										/>
										<label for="views.branches.pullRequests.enabled"
											>Show associated pull requests</label
										>
									</div>
									<p class="setting__hint">
										Requires a connection to a supported remote service (e.g. GitHub)
									</p>
								</div>

								<div class="settings settings--fixed ml-2">
									<div class="setting" data-enablement="views.branches.pullRequests.enabled">
										<div class="setting__input">
											<input
												id="views.branches.pullRequests.showForBranches"
												name="views.branches.pullRequests.showForBranches"
												type="checkbox"
												data-setting
												disabled
											/>
											<label for="views.branches.pullRequests.showForBranches"
												>Show the pull request associated with each branch</label
											>
										</div>
									</div>

									<div class="setting" data-enablement="views.branches.pullRequests.enabled">
										<div class="setting__input">
											<input
												id="views.branches.pullRequests.showForCommits"
												name="views.branches.pullRequests.showForCommits"
												type="checkbox"
												data-setting
												disabled
											/>
											<label for="views.branches.pullRequests.showForCommits"
												>Show the pull request that introduced each commit</label
											>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					<div class="setting">
						<div class="setting__input">
							<label for="views.branches.branches.layout">Layout branches</label>
							<div class="select-container">
								<select
									id="views.branches.branches.layout"
									name="views.branches.branches.layout"
									data-setting
								>
									<option value="list">as a list</option>
									<option value="tree">as a tree</option>
								</select>
							</div>
						</div>
					</div>

					<div class="setting">
						<div class="setting__input">
							<label for="views.branches.files.layout">Layout files</label>
							<div class="select-container">
								<select
									id="views.branches.files.layout"
									name="views.branches.files.layout"
									data-setting
								>
									<option value="auto">automatically</option>
									<option value="list">as a list</option>
									<option value="tree">as a tree</option>
								</select>
							</div>
						</div>
						<p class="setting__hint" data-visibility="views.branches.files.layout =auto">
							Chooses the best layout based on the number of files at each nesting level
						</p>
					</div>

					<div class="setting">
						<div class="setting__input">
							<label for="views.branches.files.icon">File icons</label>
							<div class="select-container">
								<select id="views.branches.files.icon" name="views.branches.files.icon" data-setting>
									<option value="type">show file type (default)</option>
									<option value="status">show file status</option>
								</select>
							</div>
						</div>
					</div>

					<div class="setting">
						<div class="setting__input">
							<input
								id="views.branches.files.compact"
								name="views.branches.files.compact"
								type="checkbox"
								data-setting
							/>
							<label for="views.branches.files.compact">Use compact file layout</label>
						</div>
						<p class="setting__hint">Compacts (flattens) unnecessary nesting when using a tree layouts</p>
					</div>

					<div class="setting">
						<div class="setting__input">
							<input
								id="views.branches.avatars"
								name="views.branches.avatars"
								type="checkbox"
								data-setting
							/>
							<label for="views.branches.avatars">Use author avatars</label>
						</div>
					</div>
				</div>
			</div>

			<div class="section__preview">
				<img
					class="image__preview hidden"
					src="#{webroot}/media/branches-view-compare+pr.webp"
					data-visibility="views.branches.showBranchComparison !false &amp; views.branches.pullRequests.enabled &amp; views.branches.pullRequests.showForBranches"
					loading="lazy"
					width="600"
					height="360"
				/>
				<img
					class="image__preview hidden"
					src="#{webroot}/media/branches-view-compare.webp"
					data-visibility="views.branches.showBranchComparison !false &amp; views.branches.pullRequests.enabled =false"
					loading="lazy"
					width="600"
					height="360"
				/>
				<img
					class="image__preview hidden"
					src="#{webroot}/media/branches-view-compare.webp"
					data-visibility="views.branches.showBranchComparison !false &amp; views.branches.pullRequests.enabled &amp; views.branches.pullRequests.showForBranches =false"
					loading="lazy"
					width="600"
					height="360"
				/>
				<img
					class="image__preview hidden"
					src="#{webroot}/media/branches-view-pr.webp"
					data-visibility="views.branches.showBranchComparison =false &amp; views.branches.pullRequests.enabled &amp; views.branches.pullRequests.showForBranches"
					loading="lazy"
					width="600"
					height="360"
				/>
				<img
					class="image__preview hidden"
					src="#{webroot}/media/branches-view.webp"
					data-visibility="views.branches.showBranchComparison =false &amp; views.branches.pullRequests.enabled =false"
					loading="lazy"
					width="600"
					height="360"
				/>
				<img
					class="image__preview hidden"
					src="#{webroot}/media/branches-view.webp"
					data-visibility="views.branches.showBranchComparison =false &amp; views.branches.pullRequests.enabled &amp; views.branches.pullRequests.showForBranches =false"
					loading="lazy"
					width="600"
					height="360"
				/>
			</div>
		</div>

		<div class="section__group">
			<p class="section__hint">
				<i class="icon icon__info"></i> For more options, open the
				<a
					class="secondary"
					title="Open Settings UI"
					href="command:workbench.action.openSettings?%22gitlens.views.branches%22"
					>Settings UI</a
				>
				and search for <b><i>gitlens.views.branches</i></b> or
				<b><i>gitlens.views</i></b>
			</p>
		</div>
	</div>
</section>
