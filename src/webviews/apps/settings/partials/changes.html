<section id="changes" class="section--settings section--collapsible" tabindex="-1">
	<div class="section__header">
		<h2>
			File Changes
			<a
				class="link__learn-more"
				title="Learn more"
				href="https://help.gitkraken.com/gitlens/gitlens-features/#gutter-changes"
			>
				<i class="icon icon__info"></i>
			</a>
		</h2>

		<p class="section__header-hint">
			Adds on-demand file changes annotations to highlight any local (unpublished) changes or lines changed by the
			most recent commit
		</p>
		<div class="section__header-info">
			<i class="icon icon--md icon__bulb"></i>
			<div>
				<p>
					Use the
					<span class="command hidden" data-visibility="changes.toggleMode =file">
						GitLens: Toggle File Changes Annotations
					</span>
					<a
						class="command hidden"
						title="Run command"
						href="command:gitlens.toggleFileChanges"
						data-visibility="changes.toggleMode =window"
					>
						GitLens: Toggle File Changes Annotations
					</a>
					command to turn the annotations on or off
				</p>
				<p>Press <kbd>Esc</kbd> to turn off the annotations</p>
			</div>
		</div>
	</div>

	<div class="section__collapsible">
		<div class="section__group">
			<div class="section__content">
				<div class="setting">
					<div class="setting__input">
						<label for="changes.toggleMode">Toggle annotations </label>
						<div class="select-container">
							<select id="changes.toggleMode" name="changes.toggleMode" data-setting>
								<option value="file">individually for each file</option>
								<option value="window">for all files</option>
							</select>
						</div>
					</div>
				</div>

				<div class="settings">
					<div class="setting">
						<div class="setting__input">
							<input
								id="changes.locations"
								name="changes.locations"
								type="checkbox"
								value="gutter"
								data-setting
								data-setting-type="array"
							/>
							<label for="changes.locations">Add gutter indicator</label>
						</div>
					</div>

					<div class="setting">
						<div class="setting__input">
							<input
								id="changes.locations-1"
								name="changes.locations"
								type="checkbox"
								value="line"
								data-setting
								data-setting-type="array"
							/>
							<label for="changes.locations-1">Add line highlight</label>
						</div>
					</div>

					<div class="setting">
						<div class="setting__input">
							<input
								id="changes.locations-2"
								name="changes.locations"
								type="checkbox"
								value="overview"
								data-setting
								data-setting-type="array"
							/>
							<label for="changes.locations-2">Add scroll bar indicator</label>
						</div>
					</div>
				</div>
			</div>

			<div class="section__preview">
				<img
					class="image__preview"
					src="#{webroot}/media/changes.webp"
					loading="lazy"
					width="600"
					height="206"
				/>
				<img
					class="image__preview--overlay hidden"
					src="#{webroot}/media/changes-highlight-gutter.webp"
					data-visibility="changes.locations +gutter"
					loading="lazy"
					width="600"
					height="206"
				/>
				<img
					class="image__preview--overlay hidden"
					src="#{webroot}/media/changes-highlight-scrollbar.webp"
					data-visibility="changes.locations +overview"
					loading="lazy"
					width="600"
					height="206"
				/>
			</div>
		</div>

		<div class="section__group">
			<p class="section__hint">
				<i class="icon icon__info"></i> For more options, open the
				<a
					class="secondary UI"
					title="Open Settings UI"
					href="command:workbench.action.openSettings?%22gitlens.changes%22"
					>Settings UI</a
				>
				and search for <b><i>gitlens.changes</i></b>
			</p>
		</div>
	</div>
</section>
