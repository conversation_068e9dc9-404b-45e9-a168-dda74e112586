{"cells": [{"code": "### Get Default Branch & Tip", "kind": "markdown"}, {"code": "query getDefaultBranchAndTip(\r\n\t$owner: String!\r\n\t$repo: String!\r\n) {\r\n\trepository(owner: $owner, name: $repo) {\r\n\t\tdefaultBranchRef {\r\n\t\t\tname\r\n\t\t\ttarget { oid }\r\n\t\t}\r\n\t}\r\n}\r\n\r\nvariables {\r\n\t\"owner\": \"eamodio\",\r\n\t\"repo\": \"vscode-gitlens\"\r\n}", "kind": "code"}, {"code": "### Get Branches", "kind": "markdown"}, {"code": "query getBranches(\r\n\t$owner: String!\r\n\t$repo: String!\r\n\t$branchQuery: String\r\n\t$cursor: String\r\n\t$limit: Int = 100\r\n) {\r\n\trepository(owner: $owner, name: $repo) {\r\n\t\trefs(query: $branchQuery, refPrefix: \"refs/heads/\", first: $limit, after: $cursor, orderBy: { field: TAG_COMMIT_DATE, direction: DESC }) {\r\n\t\t\tpageInfo {\r\n\t\t\t\tendCursor\r\n\t\t\t\thasNextPage\r\n\t\t\t}\r\n\t\t\tnodes {\r\n\t\t\t\tname\r\n\t\t\t\ttarget {\r\n\t\t\t\t\toid\r\n\t\t\t\t\tcommitUrl\r\n\t\t\t\t\t...on Commit {\r\n\t\t\t\t\t\tauthoredDate\r\n\t\t\t\t\t\tcommittedDate\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\nvariables {\r\n\t\"owner\": \"eamodio\",\r\n\t\"repo\": \"vscode-gitlens\"\r\n}", "kind": "code"}, {"code": "### Get Blame", "kind": "markdown"}, {"code": "query getBlame(\r\n\t$owner: String!\r\n\t$repo: String!\r\n\t$ref: String!\r\n\t$path: String!\r\n) {\r\n\tviewer { name }\r\n\trepository(owner: $owner, name: $repo) {\r\n\t\tobject(expression: $ref) {\r\n\t\t\t...on Commit {\r\n\t\t\t\tblame(path: $path) {\r\n\t\t\t\t\tranges {\r\n\t\t\t\t\t\tstartingLine\r\n\t\t\t\t\t\tendingLine\r\n\t\t\t\t\t\tage\r\n\t\t\t\t\t\tcommit {\r\n\t\t\t\t\t\t\toid\r\n\t\t\t\t\t\t\tparents(first: 3) { nodes { oid } }\r\n\t\t\t\t\t\t\tmessage\r\n\t\t\t\t\t\t\tadditions\r\n\t\t\t\t\t\t\tchangedFiles\r\n\t\t\t\t\t\t\tdeletions\r\n\t\t\t\t\t\t\tauthor {\r\n\t\t\t\t\t\t\t\tavatarUrl\r\n\t\t\t\t\t\t\t\tdate\r\n\t\t\t\t\t\t\t\temail\r\n\t\t\t\t\t\t\t\tname\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tcommitter {\r\n\t\t\t\t\t\t\t\tdate\r\n\t\t\t\t\t\t\t\temail\r\n\t\t\t\t\t\t\t\tname\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\nvariables {\r\n\t\"owner\": \"gitkraken\",\r\n\t\"repo\": \"vscode-gitlens\",\r\n\t\"ref\": \"0b8f151bd0458340b7779a64884c97754c3cedb8\",\r\n\t\"path\": \"package.json\"\r\n}", "kind": "code"}, {"code": "### Get Commit for File", "kind": "markdown"}, {"code": "query getCommitForFile(\r\n\t$owner: String!\r\n\t$repo: String!\r\n\t$ref: String!\r\n\t$path: String!\r\n) {\r\n\trepository(owner: $owner, name: $repo) {\r\n\t\tref(qualifiedName: $ref) {\r\n\t\t\ttarget {\r\n\t\t\t\t... on Commit {\r\n\t\t\t\t\thistory(first: 1, path: $path) {\r\n\t\t\t\t\t\tnodes {\r\n\t\t\t\t\t\t\toid\r\n\t\t\t\t\t\t\tparents(first: 3) { nodes { oid } }\r\n\t\t\t\t\t\t\tmessage\r\n\t\t\t\t\t\t\tadditions\r\n\t\t\t\t\t\t\tchangedFiles\r\n\t\t\t\t\t\t\tdeletions\r\n\t\t\t\t\t\t\tauthor {\r\n\t\t\t\t\t\t\t\tdate\r\n\t\t\t\t\t\t\t\temail\r\n\t\t\t\t\t\t\t\tname\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tcommitter { date }\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\nvariables {\r\n\t\"owner\": \"eamodio\",\r\n\t\"repo\": \"vscode-gitlens\",\r\n\t\"ref\": \"refs/heads/main\",\r\n\t\"path\": \"src/extension.ts\"\r\n}", "kind": "code"}, {"code": "### Get Current User", "kind": "markdown"}, {"code": "query getCurrentUser(\r\n\t$owner: String!\r\n\t$repo: String!\r\n) {\r\n\tviewer { name }\r\n\trepository(name: $repo owner: $owner) {\r\n\t\tviewerPermission\r\n\t}\r\n}\r\n\r\nvariables {\r\n\t\"owner\": \"eamodio\",\r\n\t\"repo\": \"vscode-gitlens\"\r\n}", "kind": "code"}, {"code": "### Get Commit", "kind": "markdown"}, {"code": "query getCommit(\r\n\t$owner: String!\r\n\t$repo: String!\r\n\t$ref: GitObjectID!\r\n) {\r\n\trepository(name: $repo owner: $owner) {\r\n\t\tobject(oid: $ref) {\r\n\t\t\t...on Commit {\r\n\t\t\t\toid\r\n\t\t\t\tparents(first: 3) { nodes { oid } }\r\n\t\t\t\tmessage\r\n\t\t\t\tadditions\r\n\t\t\t\tchangedFiles\r\n\t\t\t\tdeletions\r\n\t\t\t\tauthor {\r\n\t\t\t\t\tdate\r\n\t\t\t\t\temail\r\n\t\t\t\t\tname\r\n\t\t\t\t}\r\n\t\t\t\tcommitter { date }\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\nvariables {\r\n\t\"owner\": \"eamodio\",\r\n\t\"repo\": \"vscode-gitlens\",\r\n\t\"ref\": \"54f28933055124d6ba3808a787f6947c929f9db0\"\r\n}", "kind": "code"}, {"code": "### Get Commits", "kind": "markdown"}, {"code": "query getCommits(\r\n\t$owner: String!\r\n\t$repo: String!\r\n\t$ref: String!\r\n\t$path: String\r\n\t$author: CommitAuthor\r\n\t$after: String\r\n\t$before: String\r\n\t$limit: Int = 100\r\n\t$since: GitTimestamp\r\n\t$until: GitTimestamp\r\n) {\r\n\tviewer { name }\r\n\trepository(name: $repo, owner: $owner) {\r\n\t\tobject(expression: $ref) {\r\n\t\t\t... on Commit {\r\n\t\t\t\thistory(first: $limit, author: $author, path: $path, after: $after, before: $before, since: $since, until: $until) {\r\n\t\t\t\t\tpageInfo {\r\n\t\t\t\t\t\tstartCursor\r\n\t\t\t\t\t\tendCursor\r\n\t\t\t\t\t\thasNextPage\r\n\t\t\t\t\t\thasPreviousPage\r\n\t\t\t\t\t}\r\n\t\t\t\t\tnodes {\r\n\t\t\t\t\t\t... on Commit {\r\n\t\t\t\t\t\t\toid\r\n\t\t\t\t\t\t\tmessage\r\n\t\t\t\t\t\t\tparents(first: 3) { nodes { oid } }\r\n\t\t\t\t\t\t\tadditions\r\n\t\t\t\t\t\t\tchangedFiles\r\n\t\t\t\t\t\t\tdeletions\r\n\t\t\t\t\t\t\tauthor {\r\n\t\t\t\t\t\t\t\tavatarUrl\r\n\t\t\t\t\t\t\t\tdate\r\n\t\t\t\t\t\t\t\temail\r\n\t\t\t\t\t\t\t\tname\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tcommitter {\r\n\t\t\t\t\t\t\t\t date\r\n\t\t\t\t\t\t\t\t email\r\n\t\t\t\t\t\t\t\t name\r\n\t\t\t\t\t\t\t }\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\nvariables {\r\n\t\"owner\": \"eamodio\",\r\n\t\"repo\": \"vscode-gitlens\",\r\n\t\"ref\": \"HEAD\",\r\n\t\"-path\": \"src/extension.ts\",\r\n\t\"since\": \"2022-02-07T00:00:00Z\"\r\n}", "kind": "code"}, {"code": "### Get Tags", "kind": "markdown"}, {"code": "query getTags(\r\n\t$owner: String!\r\n\t$repo: String!\r\n\t$tagQuery: String\r\n\t$cursor: String\r\n\t$limit: Int = 100\r\n) {\r\n\trepository(owner: $owner, name: $repo) {\r\n\t\trefs(query: $tagQuery, refPrefix: \"refs/tags/\", first: $limit, after: $cursor, orderBy: { field: TAG_COMMIT_DATE, direction: DESC }) {\r\n\t\t\tpageInfo {\r\n\t\t\t\tendCursor\r\n\t\t\t\thasNextPage\r\n\t\t\t}\r\n\t\t\tnodes {\r\n\t\t\t\tname\r\n\t\t\t\ttarget {\r\n\t\t\t\t\toid\r\n\t\t\t\t\tcommitUrl\r\n\t\t\t\t\t...on Commit {\r\n\t\t\t\t\t\tauthoredDate\r\n\t\t\t\t\t\tcommittedDate\r\n\t\t\t\t\t\tmessage\r\n\t\t\t\t\t}\r\n\t\t\t\t\t...on Tag {\r\n\t\t\t\t\t\tmessage\r\n\t\t\t\t\t\ttagger { date }\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\nvariables {\r\n\t\"owner\": \"eamodio\",\r\n\t\"repo\": \"vscode-gitlens\"\r\n}", "kind": "code"}, {"code": "### Get collaborators ", "kind": "markdown"}, {"code": "query getCollaborators (\r\n\t$owner: String!\r\n\t$repo: String!\r\n\t$cursor: String\r\n\t$limit: Int = 100\r\n) {\r\n\trepository(owner: $owner, name: $repo) {\r\n\t\tcollaborators(affiliation: ALL, first: $limit, after: $cursor) {\r\n\t\t\tpageInfo {\r\n\t\t\t\tendCursor\r\n\t\t\t\thasNextPage\r\n\t\t\t}\r\n\t\t\tnodes {\r\n\t\t\t\tname\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\nvariables {\r\n\t\"owner\": \"eamodio\",\r\n\t\"repo\": \"vscode-gitlens\"\r\n}", "kind": "code"}, {"code": "### Resolve reference", "kind": "markdown"}, {"code": "query resolveReference(\r\n\t$owner: String!\r\n\t$repo: String!\r\n\t$ref: String!\r\n\t$path: String!\r\n) {\r\n\trepository(owner: $owner, name: $repo) {\r\n\t\tobject(expression: $ref) {\r\n\t\t\t... on Commit {\r\n\t\t\t\thistory(first: 1, path: $path) {\r\n\t\t\t\t\tnodes { oid }\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\nvariables {\r\n\t\"owner\": \"eamodio\",\r\n\t\"repo\": \"vscode-gitlens\",\r\n\t\"ref\": \"d790e9db047769de079f6838c3578f3a47bf5930^\",\r\n\t\"path\": \"CODE_OF_CONDUCT.md\"\r\n}", "kind": "code"}, {"code": "### Get branches that contain commit", "kind": "markdown"}, {"code": "query getCommitBranches(\r\n\t$owner: String!\r\n\t$repo: String!\r\n\t$since: GitTimestamp!\r\n\t$until: GitTimestamp!\r\n) {\r\n\trepository(owner: $owner, name: $repo) {\r\n\t\trefs(first: 20, refPrefix: \"refs/heads/\", orderBy: { field: TAG_COMMIT_DATE, direction: DESC }) {\r\n\t\t\tnodes {\r\n\t\t\t\tname\r\n\t\t\t\ttarget {\r\n\t\t\t\t\t... on Commit {\r\n\t\t\t\t\t\thistory(first: 3, since: $since until: $until) {\r\n\t\t\t\t\t\t\tnodes { oid }\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\nvariables {\r\n\t\"owner\": \"eamodio\",\r\n\t\"repo\": \"vscode-gitlens\",\r\n\t\"since\": \"2022-01-06T01:07:46-04:00\",\r\n\t\"until\": \"2022-01-06T01:07:46-05:00\"\r\n}", "kind": "code"}, {"code": "query getCommitBranch(\r\n\t$owner: String!\r\n\t$repo: String!\r\n\t$ref: String!\r\n\t$since: GitTimestamp!\r\n\t$until: GitTimestamp!\r\n) {\r\n\trepository(owner: $owner, name: $repo) {\r\n\t\tref(qualifiedName: $ref) {\r\n\t\t\ttarget {\r\n\t\t\t\t... on Commit {\r\n\t\t\t\t\thistory(first: 3, since: $since until: $until) {\r\n\t\t\t\t\t\tnodes { oid }\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\nvariables {\r\n\t\"owner\": \"eamodio\",\r\n\t\"repo\": \"vscode-gitlens\",\r\n\t\"ref\": \"refs/heads/main\",\r\n\t\"since\": \"2022-01-06T01:07:46-04:00\",\r\n\t\"until\": \"2022-01-06T01:07:46-05:00\"\r\n}", "kind": "code"}, {"code": "### Get commit count for branch (ref)", "kind": "markdown"}, {"code": "query getCommitCount(\r\n\t$owner: String!\r\n\t$repo: String!\r\n\t$ref: String!\r\n) {\r\n\trepository(owner: $owner, name: $repo) {\r\n\t\tref(qualifiedName: $ref) {\r\n\t\t\ttarget {\r\n\t\t\t\t... on Commit {\r\n\t\t\t\t\thistory(first: 1) {\r\n\t\t\t\t\t\ttotalCount\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\nvariables {\r\n\t\"owner\": \"eamodio\",\r\n\t\"repo\": \"vscode-gitlens\",\r\n\t\"ref\": \"refs/heads/main\"\r\n}", "kind": "code"}, {"code": "### Get commit refs (sha)", "kind": "markdown"}, {"code": "query getCommitRefs(\r\n\t$owner: String!\r\n\t$repo: String!\r\n\t$ref: String!\r\n\t$path: String\r\n\t$since: GitTimestamp\r\n\t$until: GitTimestamp\r\n\t$limit: Int = 1\r\n) {\r\n\tviewer { name }\r\n\trepository(name: $repo, owner: $owner) {\r\n\t\tref(qualifiedName: $ref) {\r\n\t\t\thistory(first: $limit, path: $path, since: $since, until: $until) {\r\n\t\t\t\tnodes { oid, message }\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\nvariables {\r\n\t\"owner\": \"eamodio\",\r\n\t\"repo\": \"vscode-gitlens\",\r\n\t\"ref\": \"refs/heads/main\",\r\n\t\"path\": \"extension.ts\",\r\n\t\"limit\": 2\r\n}", "kind": "code"}, {"code": "### Get next file commit", "kind": "markdown"}, {"code": "query getCommitDate(\r\n\t$owner: String!\r\n\t$repo: String!\r\n\t$ref: GitObjectID!\r\n) {\r\n\trepository(name: $repo owner: $owner) {\r\n\t\tobject(oid: $ref) {\r\n\t\t\t...on Commit {\r\n\t\t\t\tcommitter { date }\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\nvariables {\r\n\t\"owner\": \"eamodio\",\r\n\t\"repo\": \"vscode-gitlens\",\r\n\t\"ref\": \"a03ff942c40665c451bd4c7768f46e3e5f00e97c\"\r\n}", "kind": "code"}, {"code": "query getNextCommitCursor(\r\n\t$owner: String!\r\n\t$repo: String!\r\n\t$ref: String!\r\n\t$path: String!\r\n\t$since: GitTimestamp!\r\n) {\r\n\trepository(name: $repo owner: $owner) {\r\n\t\tobject(expression: $ref) {\r\n\t\t\t... on Commit {\r\n\t\t\t\thistory(first:1, path: $path, since: $since) {\r\n\t\t\t\t\ttotalCount\r\n\t\t\t\t\tpageInfo { startCursor }\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\nvariables {\r\n\t\"owner\": \"eamodio\",\r\n\t\"repo\": \"vscode-gitlens\",\r\n\t\"ref\": \"b062e960b6ee5ca7ac081dd84d9217bd4b2051e0\",\r\n  \"path\": \"src/extension.ts\",\r\n  \"since\": \"2021-11-03T02:46:29-04:00\"\r\n}", "kind": "code"}, {"code": "query getNextCommit(\r\n\t$owner: String!\r\n\t$repo: String!\r\n  $ref: String!\r\n  $path: String!\r\n\t$before: String!\r\n) {\trepository(name: $repo owner: $owner) {\r\n    object(expression: $ref) {\r\n      ... on Commit {\r\n        history(last:4, path: $path, before: $before) {\r\n          totalCount\r\n          pageInfo {\r\n            startCursor\r\n          }\r\n          nodes {\r\n            oid\r\n            message\r\n            committedDate\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\nvariables {\r\n\t\"owner\": \"eamodio\",\r\n\t\"repo\": \"vscode-gitlens\",\r\n\t\"ref\": \"496c35eaeff2c33d3f1256a25d83198ace6aa6b0\",\r\n  \"path\": \"src/extension.ts\",\r\n  \"before\": \"496c35eaeff2c33d3f1256a25d83198ace6aa6b0 4\"\r\n}", "kind": "code"}, {"code": "### Get Pull Request for Branch", "kind": "markdown"}, {"code": "query getPullRequestForBranch(\r\n\t$owner: String!\r\n\t$repo: String!\r\n\t$branch: String!\r\n\t$limit: Int!\r\n\t$include: [PullRequestState!]\r\n\t$avatarSize: Int\r\n) {\r\n\trepository(name: $repo, owner: $owner) {\r\n\t\trefs(query: $branch, refPrefix: \"refs/heads/\", first: 1) {\r\n\t\t\tnodes {\r\n\t\t\t\tassociatedPullRequests(first: $limit, orderBy: {field: UPDATED_AT, direction: DESC}, states: $include) {\r\n\t\t\t\t\tnodes {\r\n\t\t\t\t\t\tauthor {\r\n\t\t\t\t\t\t\tlogin\r\n\t\t\t\t\t\t\tavatarUrl(size: $avatarSize)\r\n\t\t\t\t\t\t\turl\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tpermalink\r\n\t\t\t\t\t\tnumber\r\n\t\t\t\t\t\ttitle\r\n\t\t\t\t\t\tstate\r\n\t\t\t\t\t\tcreatedAt\r\n\t\t\t\t\t\tupdatedAt\r\n\t\t\t\t\t\tclosedAt\r\n\t\t\t\t\t\tmergedAt\r\n\t\t\t\t\t\trepository {\r\n\t\t\t\t\t\t\tisFork\r\n\t\t\t\t\t\t\towner {\r\n\t\t\t\t\t\t\t\tlogin\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\nvariables {\r\n\t\"owner\": \"gitkraken\",\r\n\t\"repo\": \"vscode-gitlens\",\r\n\t\"branch\": \"main\",\r\n\t\"limit\": 1\r\n}", "kind": "code"}, {"code": "### Get My Assigned Pull Requests", "kind": "markdown"}, {"code": "query getMyAssignedPullRequests($assigned: String!) {\r\n  search(first: 100, query: $assigned, type: ISSUE) {\r\n    nodes {\r\n      ... on PullRequest {\r\n        assignees(first: 100) {\r\n          nodes {\r\n            login\r\n            avatarUrl\r\n            url\r\n          }\r\n        }\r\n        author {\r\n          login\r\n          avatarUrl\r\n          url\r\n        }\r\n        baseRefName\r\n        baseRefOid\r\n        baseRepository {\r\n          name\r\n          owner {\r\n            login\r\n          }\r\n          url\r\n        }\r\n        checksUrl\r\n        isDraft\r\n        isCrossRepository\r\n        isReadByViewer\r\n        headRefName\r\n        headRefOid\r\n        headRepository {\r\n          name\r\n          owner {\r\n            login\r\n          }\r\n          url\r\n        }\r\n        permalink\r\n        number\r\n        title\r\n        state\r\n        additions\r\n        deletions\r\n        createdAt\r\n        updatedAt\r\n        closedAt\r\n        mergeable\r\n        mergedAt\r\n        mergedBy {\r\n          login\r\n        }\r\n        reactions(content: THUMBS_UP) {\r\n          totalCount\r\n        }\r\n        repository {\r\n          isFork\r\n          owner {\r\n            login\r\n          }\r\n          viewerPermission\r\n        }\r\n        reviewDecision\r\n        reviewRequests(first: 100) {\r\n          nodes {\r\n            asCodeOwner\r\n            requestedReviewer {\r\n              ... on User {\r\n                login\r\n                avatarUrl\r\n                url\r\n              }\r\n            }\r\n          }\r\n        }\r\n        totalCommentsCount\r\n        viewerCanUpdate\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\nvariables {\r\n  \"assigned\": \"assignee:@me is:pr is:open archived:false repo:gitkraken/vscode-gitlens\"\r\n}", "kind": "code"}, {"code": "### Get My Assigned Issues", "kind": "markdown"}, {"code": "query MyQuery($assigned: String!) {\r\n  search(first: 2, query: $assigned, type: ISSUE) {\r\n    nodes {\r\n      ... on Issue {\r\n        assignees(first: 100) {\r\n          nodes {\r\n            login\r\n            url\r\n            avatarUrl\r\n          }\r\n        }\r\n        author {\r\n          login\r\n          avatarUrl\r\n          url\r\n        }\r\n        comments {\r\n          totalCount\r\n        }\r\n        number\r\n        title\r\n        url\r\n        createdAt\r\n        closedAt\r\n        closed\r\n        updatedAt\r\n        labels(first: 20) {\r\n          nodes {\r\n            color\r\n            name\r\n          }\r\n        }\r\n        reactions(content: THUMBS_UP) {\r\n          totalCount\r\n        }\r\n        repository {\r\n          name\r\n          owner {\r\n            login\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\nvariables {\r\n  \"assigned\": \"assignee:@me type:issue is:open archived:false repo:gitkraken/vscode-gitlens\"\r\n}", "kind": "code"}, {"code": "### Get PR CI Checks", "kind": "markdown"}, {"code": "query getPullRequest(\r\n\t$owner: String!\r\n\t$repo: String!\r\n\t$number: Int!\r\n) {\r\n\trepository(name: $repo, owner: $owner) {\r\n\t\tpullRequest(number: $number) {\r\n\t\t\ttitle\r\n    \t\tcommits(last: 1) {\r\n\t\t\t\tnodes {\r\n\t\t\t\t\tcommit {\r\n\t\t\t\t\t\toid\r\n\t\t\t\t\t\tstatusCheckRollup {\r\n\t\t\t\t\t\t\tstate\r\n\t\t\t\t\t\t\tcontexts(first: 10) {\r\n                \t\t\t\tedges {\r\n                  \t\t\t\t\tnode {\r\n                    \t\t\t\t\t... on CheckRun {\r\n\t\t\t\t\t\t\t\t\t\t\tname\r\n\t\t\t\t\t\t\t\t\t\t\tstatus\r\n\t\t\t\t\t\t\t\t\t\t\tconclusion\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n                \t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\nvariables {\r\n\t\"owner\": \"gitkraken\",\r\n\t\"repo\": \"vscode-gitlens\",\r\n\t\"number\": 3036\r\n}", "kind": "code"}, {"code": "### Search for Pull Request\n", "kind": "markdown"}, {"code": "query searchMyPullRequests(\n\t$search: String!\n\t$avatarSize: Int\n) {\n\trateLimit {\n    \tcost\n  \t}\n\tviewer {\n\t\tlogin\n\t}\n\tsearch(first: 100, query: $search, type: ISSUE) {\n\t\tissueCount\n\t\tnodes {\n\t\t\t...on PullRequest {\n\t\t\t\tclosed\n\t\t\t\tclosedAt\n\t\t\t\tcreatedAt\n\t\t\t\tid\n\t\t\t\tnumber\n\t\t\t\tstate\n\t\t\t\ttitle\n\t\t\t\tupdatedAt\n\t\t\t\turl\n\t\t\t\tauthor {\n\t\t\t\t\tlogin\n\t\t\t\t\tavatarUrl(size: $avatarSize)\n\t\t\t\t\turl\n\t\t\t\t}\n\t\t\t\tbaseRefName\n\t\t\t\tbaseRefOid\n\t\t\t\theadRefName\n\t\t\t\theadRefOid\n\t\t\t\theadRepository {\n\t\t\t\t\tname\n\t\t\t\t\towner {\n\t\t\t\t\t\tlogin\n\t\t\t\t\t}\n\t\t\t\t\turl\n\t\t\t\t}\n\t\t\t\tisCrossRepository\n\t\t\t\tmergedAt\n\t\t\t\tpermalink\n\t\t\t\trepository {\n\t\t\t\t\tisFork\n\t\t\t\t\tname\n\t\t\t\t\towner {\n\t\t\t\t\t\tlogin\n\t\t\t\t\t}\n\t\t\t\t\tviewerPermission\n\t\t\t\t}\n\t\t\t\tadditions\n\t\t\t\tassignees(first: 10) {\n\t\t\t\t\tnodes {\n\t\t\t\t\t\tlogin\n\t\t\t\t\t\tavatarUrl(size: $avatarSize)\n\t\t\t\t\t\turl\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tchecksUrl\n\t\t\t\tdeletions\n\t\t\t\tisDraft\n\t\t\t\tmergeable\n\t\t\t\tmergedBy {\n\t\t\t\t\tlogin\n\t\t\t\t}\n\t\t\t\tmergeable\n\t\t\t\tmergedBy {\n\t\t\t\t\tlogin\n\t\t\t\t}\n\t\t\t\treviewDecision\n\t\t\t\tlatestReviews (first: 10) {\n\t\t\t\t\tnodes {\n\t\t\t\t\t\tauthor {\n\t\t\t\t\t\t\tlogin\n\t\t\t\t\t\t\tavatarUrl(size: $avatarSize)\n\t\t\t\t\t\t\turl\n\t\t\t\t\t\t}\n\t\t\t\t\t\tstate\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treviewRequests(first: 10) {\n\t\t\t\t\tnodes {\n\t\t\t\t\t\tasCodeOwner\n\t\t\t\t\t\tid\n\t\t\t\t\t\trequestedReviewer {\n\t\t\t\t\t\t\t... on User {\n\t\t\t\t\t\t\t\tlogin\n\t\t\t\t\t\t\t\tavatarUrl(size: $avatarSize)\n\t\t\t\t\t\t\t\turl\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tstatusCheckRollup {\n\t\t\t\t\tstate\n\t\t\t\t}\n\t\t\t\ttotalCommentsCount\n\t\t\t\tviewerCanUpdate\n\t\t\t}\n\t\t}\n\t}\n}\n\nvariables {\n\t\"search\": \"is:pr is:open archived:false involves:@me\",\n\t\"avatarSize\": 16\n}\n", "kind": "code"}, {"code": "### Search for Pull Request (Lite)\n", "kind": "markdown"}, {"code": "query searchMyPullRequests(\n\t$search: String!\n\t$avatarSize: Int\n) {\n\trateLimit {\n    \tcost\n  \t}\n\tviewer {\n\t\tlogin\n\t}\n\tsearch(first: 100, query: $search, type: ISSUE) {\n\t\tissueCount\n\t\tnodes {\n\t\t\t...on PullRequest {\n\t\t\t\tclosed\n\t\t\t\tclosedAt\n\t\t\t\tcreatedAt\n\t\t\t\tid\n\t\t\t\tnumber\n\t\t\t\tstate\n\t\t\t\ttitle\n\t\t\t\tupdatedAt\n\t\t\t\turl\n\t\t\t\tauthor {\n\t\t\t\t\tlogin\n\t\t\t\t\tavatarUrl(size: $avatarSize)\n\t\t\t\t\turl\n\t\t\t\t}\n\t\t\t\tbaseRefName\n\t\t\t\tbaseRefOid\n\t\t\t\theadRefName\n\t\t\t\theadRefOid\n\t\t\t\theadRepository {\n\t\t\t\t\tname\n\t\t\t\t\towner {\n\t\t\t\t\t\tlogin\n\t\t\t\t\t}\n\t\t\t\t\turl\n\t\t\t\t}\n\t\t\t\tisCrossRepository\n\t\t\t\tmergedAt\n\t\t\t\tpermalink\n\t\t\t\trepository {\n\t\t\t\t\tisFork\n\t\t\t\t\tname\n\t\t\t\t\towner {\n\t\t\t\t\t\tlogin\n\t\t\t\t\t}\n\t\t\t\t\tviewerPermission\n\t\t\t\t}\n\t\t\t\tadditions\n\t\t\t\tassignees(first: 10) {\n\t\t\t\t\tnodes {\n\t\t\t\t\t\tlogin\n\t\t\t\t\t\tavatarUrl(size: $avatarSize)\n\t\t\t\t\t\turl\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tchecksUrl\n\t\t\t\tdeletions\n\t\t\t\tisDraft\n\t\t\t\tmergeable\n\t\t\t\tmergedBy {\n\t\t\t\t\tlogin\n\t\t\t\t}\n\t\t\t\treviewDecision\n\t\t\t\t# myReviews:reviews(states: [APPROVED, CHANGES_REQUESTED, COMMENTED] author: \"@me\") {\n\t\t\t\t# \ttotalCount\n\t\t\t\t# }\n\t\t\t\tpendingReviews:reviews(first: 10,states: [PENDING]) {\n\t\t\t\t\ttotalCount\n\t\t\t\t\tnodes {\n\t\t\t\t\t\tauthor {\n\t\t\t\t\t\t\tlogin\n\t\t\t\t\t\t\tavatarUrl(size: $avatarSize)\n\t\t\t\t\t\t\turl\n\t\t\t\t\t\t}\n\t\t\t\t\t\tstate\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tchangesRequestedReviews:reviews(states: [CHANGES_REQUESTED]) {\n\t\t\t\t\ttotalCount\n\t\t\t\t}\n\t\t\t\tcommentedReviews:reviews(states: [COMMENTED]) {\n\t\t\t\t\ttotalCount\n\t\t\t\t}\n\t\t\t\treviewRequests {\n\t\t\t\t\ttotalCount\n\t\t\t\t}\n\t\t\t\t# latestReviews(first: 10) {\n\t\t\t\t# \tnodes {\n\t\t\t\t# \t\tauthor {\n\t\t\t\t# \t\t\tlogin\n\t\t\t\t# \t\t\tavatarUrl(size: $avatarSize)\n\t\t\t\t# \t\t\turl\n\t\t\t\t# \t\t}\n\t\t\t\t# \t\tstate\n\t\t\t\t# \t}\n\t\t\t\t# }\n\t\t\t\t# reviewRequests(first: 10) {\n\t\t\t\t# \tnodes {\n\t\t\t\t# \t\tasCodeOwner\n\t\t\t\t# \t\tid\n\t\t\t\t# \t\trequestedReviewer {\n\t\t\t\t# \t\t\t... on User {\n\t\t\t\t# \t\t\t\tlogin\n\t\t\t\t# \t\t\t\tavatarUrl(size: $avatarSize)\n\t\t\t\t# \t\t\t\turl\n\t\t\t\t# \t\t\t}\n\t\t\t\t# \t\t}\n\t\t\t\t# \t}\n\t\t\t\t# }\n\t\t\t\tstatusCheckRollup {\n\t\t\t\t\tstate\n\t\t\t\t}\n\t\t\t\ttotalCommentsCount\n\t\t\t\tviewerCanUpdate\n\t\t\t\tviewerLatestReview {\n\t\t\t\t\tid\n\t\t\t\t\tstate\n\t\t\t\t}\n\t\t\t\tviewerLatestReviewRequest {\n\t\t\t\t\tid\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\nvariables {\n\t\"search\": \"is:pr is:open archived:false involves:@me\",\n\t\"avatarSize\": 16\n}\n", "kind": "code"}]}